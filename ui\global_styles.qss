/* ملف الأنماط المركزي الموحد لجميع أقسام التطبيق */
/* Global Unified Styles for All Application Sections */

/* ===== عناوين الأقسام الموحدة ===== */
.section-title {
    color: #ffffff;
    background: qlineargradient(x1:0, y1:0, x2:1, y2:1,
        stop:0 #1e40af,
        stop:0.2 #3b82f6,
        stop:0.4 #6366f1,
        stop:0.6 #8b5cf6,
        stop:0.8 #a855f7,
        stop:1 #c084fc);
    border: 3px solid #000000;
    border-radius: 10px;
    padding: 4px 10px;
    margin: 2px;
    font-weight: bold;
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 18px;
    max-height: 40px;
    min-height: 40px;
    text-align: center;
}

/* ===== الجداول الموحدة ===== */
QTableWidget {
    background-color: #ffffff;
    alternate-background-color: #f8fafc;
    gridline-color: #e2e8f0;
    border: 3px solid #000000;
    border-radius: 10px;
    selection-background-color: #3b82f6;
    selection-color: #ffffff;
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 12px;
    margin: 2px;
}

QTableWidget::item {
    padding: 8px;
    border-bottom: 1px solid #e2e8f0;
    border-right: 1px solid #e2e8f0;
}

QTableWidget::item:selected {
    background-color: #3b82f6;
    color: #ffffff;
}

QTableWidget::item:hover {
    background-color: #dbeafe;
    color: #1e40af;
}

QHeaderView::section {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #4f46e5,
        stop:0.5 #3730a3,
        stop:1 #312e81);
    color: #ffffff;
    padding: 8px 12px;
    border: 2px solid #1e1b4b;
    border-radius: 6px;
    font-weight: bold;
    font-size: 13px;
    text-align: center;
    margin: 1px;
}

QHeaderView::section:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #6366f1,
        stop:0.5 #4f46e5,
        stop:1 #3730a3);
}

/* ===== الأزرار الأساسية الموحدة ===== */
.btn-primary {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1e3a8a,
        stop:0.3 #3b82f6,
        stop:0.7 #1d4ed8,
        stop:1 #1e40af);
    color: #ffffff;
    border: 2px solid #1d4ed8;
    border-radius: 12px;
    padding: 8px 16px;
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    min-height: 32px;
    max-height: 40px;
    text-align: center;
}

.btn-primary:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #3b82f6,
        stop:0.3 #60a5fa,
        stop:0.7 #2563eb,
        stop:1 #1d4ed8);
    border: 2px solid #2563eb;
}

.btn-primary:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1e40af,
        stop:0.3 #1d4ed8,
        stop:0.7 #1e3a8a,
        stop:1 #172554);
    border: 2px solid #1e3a8a;
}

/* ===== الأزرار الخضراء (إضافة) ===== */
.btn-success {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #064e3b,
        stop:0.3 #059669,
        stop:0.7 #047857,
        stop:1 #065f46);
    color: #ffffff;
    border: 2px solid #047857;
    border-radius: 12px;
    padding: 8px 16px;
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    min-height: 32px;
    max-height: 40px;
    text-align: center;
}

.btn-success:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #059669,
        stop:0.3 #10b981,
        stop:0.7 #047857,
        stop:1 #064e3b);
    border: 2px solid #047857;
}

/* ===== الأزرار الحمراء (حذف) ===== */
.btn-danger {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #991b1b,
        stop:0.3 #dc2626,
        stop:0.7 #b91c1c,
        stop:1 #991b1b);
    color: #ffffff;
    border: 2px solid #b91c1c;
    border-radius: 12px;
    padding: 8px 16px;
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    min-height: 32px;
    max-height: 40px;
    text-align: center;
}

.btn-danger:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #dc2626,
        stop:0.3 #f87171,
        stop:0.7 #b91c1c,
        stop:1 #991b1b);
    border: 2px solid #b91c1c;
}

/* ===== الأزرار البرتقالية (تحذير) ===== */
.btn-warning {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #9a3412,
        stop:0.3 #ea580c,
        stop:0.7 #c2410c,
        stop:1 #9a3412);
    color: #ffffff;
    border: 2px solid #c2410c;
    border-radius: 12px;
    padding: 8px 16px;
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    min-height: 32px;
    max-height: 40px;
    text-align: center;
}

.btn-warning:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ea580c,
        stop:0.3 #fb923c,
        stop:0.7 #c2410c,
        stop:1 #9a3412);
    border: 2px solid #c2410c;
}

/* ===== الأزرار التيل (تحديث) ===== */
.btn-teal {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #134e4a,
        stop:0.3 #0f766e,
        stop:0.7 #0d9488,
        stop:1 #115e59);
    color: #ffffff;
    border: 2px solid #0d9488;
    border-radius: 12px;
    padding: 8px 16px;
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    min-height: 32px;
    max-height: 40px;
    text-align: center;
}

.btn-teal:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #0f766e,
        stop:0.3 #14b8a6,
        stop:0.7 #0d9488,
        stop:1 #134e4a);
    border: 2px solid #0d9488;
}

/* ===== الأزرار البنفسجية (إحصائيات) ===== */
.btn-purple {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #581c87,
        stop:0.3 #9333ea,
        stop:0.7 #7c3aed,
        stop:1 #6b21a8);
    color: #ffffff;
    border: 2px solid #7c3aed;
    border-radius: 12px;
    padding: 8px 16px;
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    min-height: 32px;
    max-height: 40px;
    text-align: center;
}

.btn-purple:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #9333ea,
        stop:0.3 #a855f7,
        stop:0.7 #7c3aed,
        stop:1 #581c87);
    border: 2px solid #7c3aed;
}

/* ===== الأزرار النيلية (تفاصيل) ===== */
.btn-indigo {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #312e81,
        stop:0.3 #4f46e5,
        stop:0.7 #4338ca,
        stop:1 #3730a3);
    color: #ffffff;
    border: 2px solid #4338ca;
    border-radius: 12px;
    padding: 8px 16px;
    font-family: 'Segoe UI', Arial, sans-serif;
    font-size: 14px;
    font-weight: bold;
    min-height: 32px;
    max-height: 40px;
    text-align: center;
}

.btn-indigo:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #4f46e5,
        stop:0.3 #6366f1,
        stop:0.7 #4338ca,
        stop:1 #312e81);
    border: 2px solid #4338ca;
}

/* ===== حقول الإدخال الموحدة ===== */
QLineEdit {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff,
        stop:0.5 #f8fafc,
        stop:1 #e2e8f0);
    border: 3px solid #4f46e5;
    border-radius: 12px;
    padding: 8px 15px;
    font-size: 14px;
    font-weight: bold;
    color: #1f2937;
    max-height: 38px;
    min-height: 34px;
    selection-background-color: #4f46e5;
}

QLineEdit:focus {
    border: 3px solid #3730a3;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f0f9ff,
        stop:1 #e0f2fe);
}

QLineEdit:hover {
    border: 3px solid #5b52f0;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #fafbff,
        stop:1 #f1f5f9);
}

/* ===== القوائم المنسدلة الموحدة ===== */
QComboBox {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff,
        stop:0.5 #f8fafc,
        stop:1 #e2e8f0);
    border: 3px solid #4f46e5;
    border-radius: 12px;
    padding: 8px 15px;
    font-size: 14px;
    font-weight: bold;
    color: #1f2937;
    min-height: 34px;
}

QComboBox:hover {
    border: 3px solid #5b52f0;
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #fafbff,
        stop:1 #f1f5f9);
}

QComboBox::drop-down {
    border: none;
    width: 30px;
}

QComboBox::down-arrow {
    image: none;
    border: 2px solid #4f46e5;
    width: 12px;
    height: 12px;
    background: #4f46e5;
}
