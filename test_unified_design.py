#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار التصميم الموحد
Test Unified Design System
"""

import sys
import os
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QLabel, QTableWidget, QTableWidgetItem,
                            QTabWidget, QPushButton)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont

# إضافة مسار المشروع
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from ui.unified_styles import (
        AdvancedStyledButton, 
        apply_section_title_style,
        UnifiedStyleManager,
        StyledTable,
        apply_global_styles
    )
    print("✅ تم استيراد مكتبات التصميم الموحد بنجاح")
except ImportError as e:
    print(f"❌ خطأ في استيراد مكتبات التصميم: {e}")
    sys.exit(1)

class UnifiedDesignTestWindow(QMainWindow):
    """نافذة اختبار التصميم الموحد"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("اختبار التصميم الموحد - Unified Design Test")
        self.setGeometry(100, 100, 1200, 800)
        
        # إنشاء الواجهة
        self.init_ui()
        
        # تطبيق التصميم الموحد
        self.apply_unified_design()
    
    def init_ui(self):
        """إنشاء واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout()
        central_widget.setLayout(main_layout)
        
        # العنوان الرئيسي
        self.main_title = QLabel("🎨 اختبار التصميم الموحد - جميع العناصر")
        self.main_title.setFont(QFont("Segoe UI", 18, QFont.Bold))
        self.main_title.setAlignment(Qt.AlignCenter)
        main_layout.addWidget(self.main_title)
        
        # إنشاء تبويبات للاختبار
        self.tabs = QTabWidget()
        main_layout.addWidget(self.tabs)
        
        # تبويب الأزرار
        self.create_buttons_tab()
        
        # تبويب الجداول
        self.create_tables_tab()
        
        # تبويب العناوين
        self.create_titles_tab()
    
    def create_buttons_tab(self):
        """إنشاء تبويب اختبار الأزرار"""
        buttons_widget = QWidget()
        layout = QVBoxLayout()
        buttons_widget.setLayout(layout)
        
        # عنوان التبويب
        title = QLabel("🔘 اختبار الأزرار المتطورة")
        title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        apply_section_title_style(title)
        layout.addWidget(title)
        
        # مجموعة الأزرار الأساسية
        basic_layout = QHBoxLayout()
        
        # إنشاء أزرار بألوان مختلفة
        buttons_data = [
            ("➕ إضافة", 'emerald', False),
            ("✏️ تعديل", 'primary', False),
            ("🗑️ حذف", 'red', False),
            ("🔄 تحديث", 'teal', False),
            ("📤 تصدير", 'orange', True),
            ("📊 إحصائيات", 'purple', True),
            ("👁️ تفاصيل", 'indigo', True)
        ]
        
        self.test_buttons = []
        for text, color, has_menu in buttons_data:
            btn = AdvancedStyledButton(text, color, has_menu)
            btn.clicked.connect(lambda checked, t=text: self.button_clicked(t))
            basic_layout.addWidget(btn.button)
            self.test_buttons.append(btn)
        
        layout.addLayout(basic_layout)
        
        # رسالة النتيجة
        self.button_result = QLabel("اضغط على أي زر لاختباره...")
        self.button_result.setAlignment(Qt.AlignCenter)
        self.button_result.setStyleSheet("""
            QLabel {
                background-color: #f3f4f6;
                border: 2px solid #d1d5db;
                border-radius: 8px;
                padding: 15px;
                font-size: 14px;
                color: #374151;
                margin: 10px;
            }
        """)
        layout.addWidget(self.button_result)
        
        layout.addStretch()
        self.tabs.addTab(buttons_widget, "🔘 الأزرار")
    
    def create_tables_tab(self):
        """إنشاء تبويب اختبار الجداول"""
        tables_widget = QWidget()
        layout = QVBoxLayout()
        tables_widget.setLayout(layout)
        
        # عنوان التبويب
        title = QLabel("📋 اختبار الجداول الموحدة")
        title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        title.setAlignment(Qt.AlignCenter)
        apply_section_title_style(title)
        layout.addWidget(title)
        
        # إنشاء جدول باستخدام StyledTable
        styled_table = StyledTable()
        self.test_table = styled_table.table
        
        # إعداد الجدول
        self.test_table.setColumnCount(4)
        self.test_table.setHorizontalHeaderLabels(["الاسم", "النوع", "الحالة", "التاريخ"])
        
        # إضافة بيانات تجريبية
        test_data = [
            ["عنصر تجريبي 1", "نوع أ", "نشط", "2024-01-15"],
            ["عنصر تجريبي 2", "نوع ب", "غير نشط", "2024-01-16"],
            ["عنصر تجريبي 3", "نوع ج", "نشط", "2024-01-17"],
            ["عنصر تجريبي 4", "نوع أ", "معلق", "2024-01-18"],
            ["عنصر تجريبي 5", "نوع ب", "نشط", "2024-01-19"]
        ]
        
        self.test_table.setRowCount(len(test_data))
        for row, data in enumerate(test_data):
            for col, value in enumerate(data):
                item = QTableWidgetItem(str(value))
                self.test_table.setItem(row, col, item)
        
        layout.addWidget(self.test_table)
        
        # معلومات الجدول
        table_info = QLabel("✅ هذا الجدول يستخدم التصميم الموحد من StyledTable")
        table_info.setAlignment(Qt.AlignCenter)
        table_info.setStyleSheet("""
            QLabel {
                background-color: #dcfce7;
                border: 2px solid #16a34a;
                border-radius: 8px;
                padding: 10px;
                font-size: 13px;
                color: #15803d;
                margin: 5px;
            }
        """)
        layout.addWidget(table_info)
        
        self.tabs.addTab(tables_widget, "📋 الجداول")
    
    def create_titles_tab(self):
        """إنشاء تبويب اختبار العناوين"""
        titles_widget = QWidget()
        layout = QVBoxLayout()
        titles_widget.setLayout(layout)
        
        # عناوين مختلفة
        titles_data = [
            "🏠 عنوان القسم الرئيسي",
            "👥 إدارة العملاء",
            "🏭 إدارة الموردين", 
            "💰 إدارة المصروفات",
            "📋 إدارة الفواتير",
            "💵 إدارة الإيرادات",
            "👷 إدارة الموظفين",
            "📦 إدارة المخزون"
        ]
        
        for title_text in titles_data:
            title = QLabel(title_text)
            title.setFont(QFont("Segoe UI", 16, QFont.Bold))
            title.setAlignment(Qt.AlignCenter)
            apply_section_title_style(title)
            layout.addWidget(title)
        
        # معلومات العناوين
        titles_info = QLabel("✅ جميع العناوين أعلاه تستخدم apply_section_title_style")
        titles_info.setAlignment(Qt.AlignCenter)
        titles_info.setStyleSheet("""
            QLabel {
                background-color: #dbeafe;
                border: 2px solid #3b82f6;
                border-radius: 8px;
                padding: 10px;
                font-size: 13px;
                color: #1d4ed8;
                margin: 10px;
            }
        """)
        layout.addWidget(titles_info)
        
        layout.addStretch()
        self.tabs.addTab(titles_widget, "📝 العناوين")
    
    def apply_unified_design(self):
        """تطبيق التصميم الموحد على النافذة"""
        try:
            # تطبيق الأنماط المركزية
            apply_global_styles(self)
            
            # تطبيق نمط العنوان الرئيسي
            apply_section_title_style(self.main_title)
            
            print("✅ تم تطبيق التصميم الموحد على نافذة الاختبار")
            
        except Exception as e:
            print(f"❌ خطأ في تطبيق التصميم الموحد: {e}")
    
    def button_clicked(self, button_text):
        """معالج النقر على الأزرار"""
        self.button_result.setText(f"✅ تم النقر على: {button_text}")
        self.button_result.setStyleSheet("""
            QLabel {
                background-color: #dcfce7;
                border: 2px solid #16a34a;
                border-radius: 8px;
                padding: 15px;
                font-size: 14px;
                color: #15803d;
                margin: 10px;
            }
        """)

def main():
    """الدالة الرئيسية للاختبار"""
    app = QApplication(sys.argv)
    
    # تعيين اتجاه النص للعربية
    app.setLayoutDirection(Qt.RightToLeft)
    
    # تعيين الخط
    font = QFont("Segoe UI", 10)
    app.setFont(font)
    
    # إنشاء نافذة الاختبار
    window = UnifiedDesignTestWindow()
    window.show()
    
    print("🚀 تم تشغيل اختبار التصميم الموحد")
    print("📝 تحقق من:")
    print("   - ألوان وأشكال الأزرار")
    print("   - تصميم الجداول")
    print("   - أنماط العناوين")
    print("   - التناسق العام")
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
