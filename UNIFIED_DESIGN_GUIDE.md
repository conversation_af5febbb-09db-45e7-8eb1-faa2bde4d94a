# دليل التصميم الموحد للتطبيق
## Unified Design Guide for Application

### 📋 نظرة عامة
تم إنشاء نظام تصميم موحد لضمان التناسق البصري عبر جميع أقسام التطبيق. يشمل هذا النظام:
- عناوين موحدة لجميع الأقسام
- أزرار بألوان وأنماط متسقة
- جداول بتصميم موحد
- ملف QSS مركزي للأنماط

### 🎨 الملفات الأساسية

#### 1. `ui/unified_styles.py`
الملف الرئيسي الذي يحتوي على:
- فئة `UnifiedStyles` مع جميع الأنماط الأساسية
- فئة `AdvancedStyledButton` للأزرار المتطورة
- فئة `StyledTable` للجداول الموحدة
- فئة `UnifiedStyleManager` لإدارة التصميم

#### 2. `ui/global_styles.qss`
ملف QSS مركزي يحتوي على:
- أنماط العناوين (.section-title)
- أنماط الجداول (QTableWidget)
- أنماط الأزرار (.btn-primary, .btn-success, إلخ)
- أنماط حقول الإدخال (QLineEdit, QComboBox)

### 🔧 كيفية تطبيق التصميم الموحد

#### للعناوين:
```python
from ui.unified_styles import apply_section_title_style

title_label = QLabel("عنوان القسم")
title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
title_label.setAlignment(Qt.AlignCenter)
apply_section_title_style(title_label)
```

#### للأزرار المتطورة:
```python
from ui.unified_styles import AdvancedStyledButton

# أزرار بألوان مختلفة
add_button = AdvancedStyledButton("➕ إضافة", 'emerald', has_menu=True)
edit_button = AdvancedStyledButton("✏️ تعديل", 'primary')
delete_button = AdvancedStyledButton("🗑️ حذف", 'red')
refresh_button = AdvancedStyledButton("🔄 تحديث", 'teal')
export_button = AdvancedStyledButton("📤 تصدير", 'orange')
stats_button = AdvancedStyledButton("📊 إحصائيات", 'purple')
details_button = AdvancedStyledButton("👁️ تفاصيل", 'indigo')

# إضافة الأزرار للتخطيط
layout.addWidget(add_button.button)
layout.addWidget(edit_button.button)
```

#### للجداول:
```python
from ui.unified_styles import StyledTable, UnifiedStyleManager

# الطريقة الأولى: استخدام StyledTable
styled_table = StyledTable()
self.table = styled_table.table

# الطريقة الثانية: تطبيق النمط على جدول موجود
UnifiedStyleManager.setup_table(existing_table)
```

#### لتطبيق الأنماط المركزية على قسم كامل:
```python
from ui.unified_styles import UnifiedStyleManager

class MyWidget(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        
        # تطبيق التصميم الموحد على القسم
        UnifiedStyleManager.setup_section(
            widget=self,
            title_text="اسم القسم",
            title_widget=self.title_label
        )
```

### 🎯 ألوان الأزرار المتاحة

| اللون | الاستخدام | المثال |
|--------|-----------|--------|
| `primary` | العمليات الأساسية | تعديل، عرض |
| `emerald` | الإضافة | إضافة عنصر جديد |
| `red` | الحذف والخطر | حذف، إلغاء |
| `orange` | التحذير والتصدير | تصدير، تحذير |
| `teal` | التحديث | تحديث البيانات |
| `purple` | الإحصائيات | عرض الإحصائيات |
| `indigo` | التفاصيل | عرض التفاصيل |

### 📁 الأقسام المحدثة

تم تطبيق التصميم الموحد على الأقسام التالية:

#### ✅ مكتمل:
- **المصروفات** (`ui/expenses.py`)
- **الفواتير** (`ui/invoices.py`) 
- **الإيرادات** (`ui/revenues.py`)
- **الموظفين** (`ui/employees.py`)
- **المخزون** (`ui/inventory.py`)

#### 🔄 يستخدم التصميم الموحد مسبقاً:
- **العملاء** (`ui/clients.py`)
- **الموردين** (`ui/suppliers.py`)

### 🚀 التطبيق التلقائي

تم إضافة التطبيق التلقائي للأنماط في `main.py`:

```python
# تطبيق الأنماط المركزية الموحدة على جميع الأقسام
try:
    from ui.unified_styles import UnifiedStyleManager
    UnifiedStyleManager.apply_to_all_sections(app)
    print("🎨 تم تطبيق التصميم الموحد على جميع أقسام التطبيق")
except Exception as e:
    print(f"⚠️ تحذير: فشل في تطبيق الأنماط المركزية: {str(e)}")
```

### 🔧 إضافة قسم جديد

لإضافة قسم جديد بالتصميم الموحد:

1. **استيراد المكتبات المطلوبة:**
```python
from ui.unified_styles import (
    AdvancedStyledButton, 
    apply_section_title_style,
    UnifiedStyleManager,
    StyledTable
)
```

2. **إنشاء العنوان:**
```python
title_label = QLabel("📋 عنوان القسم الجديد")
title_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
title_label.setAlignment(Qt.AlignCenter)
apply_section_title_style(title_label)
```

3. **إنشاء الأزرار:**
```python
add_btn = AdvancedStyledButton("➕ إضافة", 'emerald')
edit_btn = AdvancedStyledButton("✏️ تعديل", 'primary')
delete_btn = AdvancedStyledButton("🗑️ حذف", 'red')

# إضافة للتخطيط
layout.addWidget(add_btn.button)
layout.addWidget(edit_btn.button)
layout.addWidget(delete_btn.button)
```

4. **إنشاء الجدول:**
```python
styled_table = StyledTable()
self.table = styled_table.table
```

5. **تطبيق التصميم الموحد:**
```python
UnifiedStyleManager.setup_section(self, "اسم القسم", title_label)
```

### 🎨 تخصيص الألوان

يمكن إضافة ألوان جديدة في `get_advanced_button_style()` في ملف `unified_styles.py`:

```python
'new_color': {
    'bg_start': '#color1',
    'bg_mid': '#color2', 
    'bg_end': '#color3',
    'bg_bottom': '#color4',
    # ... باقي الألوان
}
```

### 📝 ملاحظات مهمة

1. **استخدم دائماً** `AdvancedStyledButton` للأزرار الجديدة
2. **طبق** `apply_section_title_style` على جميع عناوين الأقسام
3. **استخدم** `StyledTable` للجداول الجديدة
4. **تأكد** من استيراد المكتبات المطلوبة
5. **اختبر** التصميم بعد التطبيق

### 🔍 استكشاف الأخطاء

إذا لم يظهر التصميم بشكل صحيح:

1. تأكد من وجود ملف `ui/global_styles.qss`
2. تحقق من الاستيرادات في بداية الملف
3. تأكد من استخدام `.button` عند إضافة `AdvancedStyledButton` للتخطيط
4. راجع رسائل الخطأ في وحدة التحكم

### 📞 الدعم

للمساعدة في تطبيق التصميم الموحد، راجع:
- ملف `ui/unified_styles.py` للأمثلة
- الأقسام المحدثة كمرجع
- هذا الدليل للتعليمات التفصيلية
